# run_gcode_printer.py
# 这是一个功能完整的、用于执行G-code的机器人3D打印程序。
# 它实现了连接、上电、移动到安全起点、坐标系平移、执行G-code轨迹、下电和断开的完整流程。

import sys
import os
import time
import re

# --- 步骤1：项目路径设置 ---
# 向上回溯两级目录找到项目根目录 (hardware -> tests -> INEXBOT_3DP)
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
# 将项目根目录添加到Python路径中，以便能找到config和src
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

# 将 lib 目录也明确添加到路径中
LIB_PATH = os.path.join(PROJECT_ROOT, 'lib')
if LIB_PATH not in sys.path:
    sys.path.insert(0, LIB_PATH)


# --- 步骤2：导入所有必要的模块 ---
try:
    from config import ROBOT_IP, ROBOT_PORT
    from src.hardware.robot_interface import RobotInterface
    import nrc_interface as nrc #
    print("✅ 成功导入所有必要的模块。")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print("请确保您的项目结构正确，且在根目录下有config.py和src/hardware/robot_interface.py文件。")
    exit()

# G-code数据
GCODE_DATA = """
; === 第二部分：自定义算法生成的上层（6轴） ===
G0 X104.2890 Y104.5720 Z13.5480 A0.000 B0.000 C0.000 F21000 ; Initial position and orientation
; -- Path Data Start --
; ---- Preparing Path Segment 1, OriginalID: 100001, Type: BOUNDARY, Points: 25 ----
G0 X84.4676 Y187.0916 Z13.5480 F21000 ; Move to Pre-Approach XY (Safe Z)
G0 X84.4676 Y187.0916 Z3.5587 F21000 ; Lower to Pre-Approach Z
G0 A38.811 B0.000 C0.609 F5000 ; Reorient at Pre-Approach Point
G1 E0.80000 F1800 ; Prime (Klipper M83)
; Printing Path Segment 1
G1 X84.4543 Y188.3451 Z2.0002 A38.811 B0.000 C0.609 F3000
G1 X324.4407 Y190.8885 Z2.0002 E17.56135 A38.656 B0.000 C0.607 F3000
G1 X324.4265 Y192.2436 Z3.0842 E0.12698 A34.262 B0.000 C0.606 F3000
G1 X324.4114 Y193.6447 Z3.8889 E0.11823 A25.578 B0.000 C0.605 F3000
G1 X324.3957 Y195.1501 Z4.4755 E0.11823 A17.000 B0.000 C0.603 F3000
G1 X324.3790 Y196.7261 Z4.8310 E0.11822 A8.437 B0.000 C0.600 F3000
G1 X324.3620 Y198.3205 Z4.9471 E0.11698 A-0.022 B2.082 C-90.000 F3000
G1 X324.2026 Y213.3727 Z4.9470 E1.10146 A0.023 B-2.168 C-90.000 F3000
G1 X324.1850 Y215.0207 Z4.8220 E0.12094 A8.682 B-0.000 C-179.387 F3000
G1 X324.1683 Y216.5948 Z4.4578 E0.11823 A17.317 B-0.000 C-179.389 F3000
G1 X324.1524 Y218.0969 Z3.8628 E0.11823 A25.897 B-0.000 C-179.390 F3000
G1 X324.1376 Y219.4934 Z3.0504 E0.11822 A34.500 B-0.000 C-179.391 F3000
G1 X324.1237 Y220.7989 Z2.0002 E0.12260 A38.811 B-0.000 C-179.391 F3000
G1 X84.1373 Y218.2555 Z2.0002 E17.56135 A38.656 B-0.000 C-179.393 F3000
G1 X84.1515 Y216.9004 Z3.0842 E0.12698 A34.262 B-0.000 C-179.394 F3000
G1 X84.1666 Y215.4993 Z3.8889 E0.11823 A25.579 B-0.000 C-179.395 F3000
G1 X84.1823 Y213.9940 Z4.4755 E0.11822 A17.000 B-0.000 C-179.397 F3000
G1 X84.1990 Y212.4179 Z4.8310 E0.11823 A8.400 B-0.000 C-179.400 F3000
G1 X84.2166 Y210.7686 Z4.9489 E0.12100 A0.015 B-2.040 C-90.000 F3000
G1 X84.3749 Y195.8202 Z4.9483 E1.09387 A-0.027 B2.125 C-90.000 F3000
G1 X84.3930 Y194.1233 Z4.8220 E0.12451 A8.642 B0.000 C0.609 F3000
G1 X84.4097 Y192.5494 Z4.4578 E0.11822 A17.318 B0.000 C0.611 F3000
G1 X84.4256 Y191.0472 Z3.8628 E0.11823 A25.897 B0.000 C0.610 F3000
G1 X84.4404 Y189.6506 Z3.0504 E0.11823 A34.499 B0.000 C0.609 F3000
G1 X84.4543 Y188.3451 Z2.0002 E0.12260 A38.811 B0.000 C0.609 F3000
; Path Segment 1 Print End
"""

class GCodeExecutor:
    """解析并执行G-code，驱动机器人进行3D打印运动"""

    def __init__(self, ip, port):
        self.robot_ip = ip
        self.robot_port = port
        self.robot: RobotInterface = None
        self.socket_fd = -1
        # 初始化状态变量，用于G-code的增量更新
        self.last_pose = [0.0] * 6  # [X, Y, Z, A, B, C]
        self.last_feedrate = 3000.0 # 默认速度, mm/min

    def connect_and_power_on(self):
        """连接并上电机器人"""
        print(f"--- 正在连接机器人 {self.robot_ip}:{self.robot_port} ---")
        try:
            self.robot = RobotInterface(ROBOT_IP, ROBOT_PORT)
            self.socket_fd = self.robot.socket_fd
            if self.socket_fd < 0:
                print("❌ RobotInterface 初始化失败。")
                return False

            print("\n--- 开始机器人初始化和上电流程 ---")
            nrc.clear_error(self.socket_fd) #
            time.sleep(0.2)
            
            result = nrc.get_servo_state(self.socket_fd, 0) #
            current_state = result[1] if isinstance(result, list) else -1
            
            if current_state == 0:
                nrc.set_servo_state(self.socket_fd, 1) #
                time.sleep(0.2)

            ret_code = nrc.set_servo_poweron(self.socket_fd) #
            if ret_code != 0:
                print(f"❌ 上电失败！返回码: {ret_code}。请检查安全回路/急停/远程模式。")
                return False
            
            time.sleep(1)
            result = nrc.get_servo_state(self.socket_fd, 0) #
            if result[1] == 3:
                print("✅ 机器人已上电，进入运行状态。")
                return True
            else:
                print("❌ 上电后状态异常。")
                return False
        except Exception as e:
            print(f"❌ 连接或上电时发生异常: {e}")
            return False

    def disconnect_and_power_off(self):
        """下电并断开连接"""
        if self.robot and self.socket_fd >= 0:
            if nrc.get_connection_status(self.socket_fd) == 0: #
                print("\n--- 开始机器人下电流程 ---")
                try:
                    # 在下电前先停止所有运动并清空队列
                    nrc.queue_motion_stop(self.socket_fd) #
                    nrc.queue_motion_clear_Data(self.socket_fd) #
                    time.sleep(0.1)
                    nrc.set_servo_poweroff(self.socket_fd) #
                    time.sleep(0.5)
                    print("✅ 机器人已安全下电。")
                except Exception as e:
                    print(f"❌ 下电时发生异常: {e}")
            self.robot.disconnect()

    def parse_gcode_line(self, line: str) -> dict:
        """解析单行G-code，返回参数字典"""
        params = {}
        line = line.split(';')[0].strip()
        if not line: return None

        parts = line.split()
        if not parts: return None
            
        command = parts[0].upper()
        if command in ['G0', 'G1']:
            params['G'] = int(command[1:])
        else:
             params['G'] = -1

        for part in parts[1:]:
            try:
                letter = part[0].upper()
                value = float(part[1:])
                params[letter] = value
            except (ValueError, IndexError):
                continue
        return params

    def handle_extrusion(self, e_value):
        """处理挤出机指令的占位符函数"""
        if e_value is not None:
             # 在这里，您需要添加控制挤出机的代码
             # 例如，通过另一个串口、IO或网络协议发送指令
             print(f"  -> (模拟)挤出机指令: E={e_value:.5f}")
             pass

    def run_gcode(self, gcode_data: str):
        """执行G-code主函数，包含坐标系平移逻辑"""
        print("\n--- 开始执行G-code（带坐标系平移） ---")
        
        # 步骤1: 获取机器人当前位置作为基准点
        print("\n步骤 1: 获取机器人当前位置作为基准点...")
        base_pose_container = nrc.VectorDouble() #
        if nrc.get_current_position(self.socket_fd, 1, base_pose_container) != 0: #
            print("❌ 获取基准点失败，任务中止。")
            return
        base_pose = [base_pose_container[i] for i in range(len(base_pose_container))]
        print(f"  -> 基准点位姿: {[f'{p:.3f}' for p in base_pose]}")

        # 步骤2: 计算G-code在机器人世界坐标系下的执行起点 (-Z 30mm)
        actual_robot_start_pose = list(base_pose)
        actual_robot_start_pose[2] -= 30.0
        print(f"步骤 2: 计算出G-code执行起点: {[f'{p:.3f}' for p in actual_robot_start_pose]}")

        # 步骤3: 使用单指令模式，移动到安全的执行起点
        print("\n步骤 3: 正在移动到G-code执行起点...")
        move_cmd = nrc.MoveCmd() #
        move_cmd.velocity = 100
        move_cmd.pl = 1
        move_cmd.coord = 1
        move_cmd.targetPosValue = nrc.VectorDouble(actual_robot_start_pose) #
        if nrc.robot_movel(self.socket_fd, move_cmd) != 0: #
            print("❌ 移动到执行起点失败，任务中止。")
            return
        print("✅ 已到达G-code执行起点。")
        time.sleep(1)

        # 步骤4: 启用队列运动模式，准备执行G-code轨迹
        print("\n步骤 4: 启用队列模式，准备流式发送G-code轨迹...")
        if nrc.queue_motion_set_status(self.socket_fd, True) != 0: #
            print("❌ 启用队列模式失败！")
            return
        nrc.queue_motion_clear_Data(self.socket_fd) #
        print("✅ 队列运动模式已启用并清空。")

        # 找到G-code自身的原点（第一个G0/G1的坐标）用于计算偏移
        gcode_origin_pose = None
        lines = gcode_data.strip().split('\n')
        for line in lines:
            params = self.parse_gcode_line(line)
            if params and params.get('G') in [0, 1] and 'X' in params: # 确保是完整的运动指令
                gcode_origin_pose = [
                    params.get('X', 0), params.get('Y', 0), params.get('Z', 0),
                    params.get('A', 0), params.get('B', 0), params.get('C', 0)
                ]
                print(f"  -> 已定义G-code内部原点为: {[f'{p:.3f}' for p in gcode_origin_pose]}")
                break
        
        if gcode_origin_pose is None:
            print("❌ G-code中未找到任何有效的运动指令，任务中止。")
            return

        # 循环处理每一行G-code
        self.last_pose = list(gcode_origin_pose)
        for i, line in enumerate(lines):
            params = self.parse_gcode_line(line)
            if not params: continue

            # 更新速度
            if 'F' in params: self.last_feedrate = params['F']
            move_cmd.velocity = self.last_feedrate / 60.0

            # 如果是G0/G1运动指令，则计算并发送
            if params.get('G') in [0, 1]:
                print(f"\n[处理行 {i+1}]: {line.strip()}")
                
                # 更新G-code位姿
                pose_map = {'X': 0, 'Y': 1, 'Z': 2, 'A': 3, 'B': 4, 'C': 5}
                for axis, index in pose_map.items():
                    if axis in params: self.last_pose[index] = params[axis]
                
                # 计算坐标变换
                final_target_pose = [0.0] * 6
                for j in range(6):
                    offset = self.last_pose[j] - gcode_origin_pose[j]
                    final_target_pose[j] = actual_robot_start_pose[j] + offset
                
                # 发送变换后的坐标到队列
                move_cmd.targetPosValue = nrc.VectorDouble(final_target_pose) #
                if nrc.queue_motion_push_back_moveL(self.socket_fd, move_cmd) != 0: #
                    print(f"❌ 发送指令到队列失败!")
                    break
                print(f"  -> 发送变换后目标至队列: {[f'{p:.3f}' for p in final_target_pose]}")

            # 独立处理挤出机指令
            self.handle_extrusion(params.get('E'))
        
        # 等待所有队列指令执行完毕
        print("\n--- 所有指令已发送至队列，等待机器人执行完成... ---")
        while True:
            queue_len_container = nrc.VectorInt(1) #
            nrc.queue_motion_get_queuelen(self.socket_fd, queue_len_container) #
            if queue_len_container[0] == 0:
                print("✅ 机器人运动队列已清空，任务完成。")
                break
            print(f"  -> 队列中剩余 {queue_len_container[0]} 条指令...")
            time.sleep(1)
        
        nrc.queue_motion_set_status(self.socket_fd, False) #

def main():
    """主程序入口"""
    print("=" * 60)
    print("           六轴机器人 G-code 3D打印程序")
    print("=" * 60)
    print("\n⚠️  警告：请确保机器人周围有足够的安全空间，程序即将开始！\n")
    time.sleep(3)

    executor = GCodeExecutor(ROBOT_IP, ROBOT_PORT)
    
    try:
        if executor.connect_and_power_on():
            executor.run_gcode(GCODE_DATA)
    except Exception as e:
        print(f"\n❌ 程序执行过程中发生意外错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        executor.disconnect_and_power_off()
        print("\n程序结束。")

if __name__ == "__main__":
    main()