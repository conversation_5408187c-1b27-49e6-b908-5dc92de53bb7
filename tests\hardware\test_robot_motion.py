# tests/hardware/test_robot_motion.py

import sys
import os
import time

# --- 项目路径设置 (与您之前的代码相同) ---
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
sys.path.insert(0, PROJECT_ROOT)
LIB_PATH = os.path.join(PROJECT_ROOT, 'lib')
if LIB_PATH not in sys.path:
    sys.path.insert(0, LIB_PATH)

# --- 导入 ---
from config import ROBOT_IP, ROBOT_PORT
from src.hardware.robot_interface import RobotInterface
try:
    import nrc_interface as nrc
    print("成功导入nrc_interface模块")
except ImportError as e:
    print(f"导入nrc_interface失败: {e}")
    exit()

class RobotMotionTester:
    """一个专门用于执行和验证机器人运动功能的类"""

    def __init__(self, robot_interface: RobotInterface):
        self.robot = robot_interface
        self.socket_fd = robot_interface.socket_fd

    def robot_initialize_and_power_on(self):
        """健壮的机器人初始化和上电流程"""
        print("\n--- 开始机器人初始化和上电流程 ---")
        try:
            # 步骤1: 清除错误
            print("步骤 1: 清除错误...")
            nrc.clear_error(self.socket_fd)
            time.sleep(0.5)

            # 步骤2: 获取当前伺服状态 (*** 此处为关键修正点 ***)
            print("步骤 2: 获取当前伺服状态...")
            # 直接调用函数，它会返回一个列表 [return_code, value]
            result = nrc.get_servo_state(self.socket_fd, 0) # 传入一个临时的整数0
            
            if isinstance(result, list) and len(result) > 1:
                ret_code = result[0]
                current_state = result[1]
            else:
                print(f"错误: 获取伺服状态返回格式不正确: {result}")
                return False

            if ret_code != 0:
                print(f"错误: 获取伺服状态API调用失败，返回码: {ret_code}")
                return False
                
            print(f"  -> 当前伺服状态为 {current_state}")

            # 步骤3: 根据状态进入就绪态(1) (后续逻辑不变)
            if current_state == 0:
                nrc.set_servo_state(self.socket_fd, 1)
                time.sleep(0.5)
            elif current_state == 3:
                nrc.set_servo_poweroff(self.socket_fd)
                time.sleep(1)

            # ... 后续上电逻辑 ...
            # (为简洁起见，省略了与之前版本相同的上电部分)
            # ... 请确保这部分逻辑在您的代码中是完整的 ...
            print("步骤 4: 执行上电操作...")
            ret_code = nrc.set_servo_poweron(self.socket_fd)
            if ret_code != 0:
                print(f"上电失败！返回码: {ret_code}。请检查安全回路/急停/远程模式。")
                return False

            time.sleep(1)
            
            # 最后再次确认状态
            result = nrc.get_servo_state(self.socket_fd, 0)
            final_state = result[1] if isinstance(result, list) and len(result) > 1 else -1

            if final_state == 3:
                print(f"✅ 成功！机器人已上电，进入运行状态 (3)。")
                return True
            else:
                print(f"错误: 上电后状态异常，当前: {final_state}")
                return False

        except Exception as e:
            print(f"初始化和上电流程中发生异常: {e}")
            return False

    def get_cartesian_position(self):
        """获取并返回当前的笛卡尔坐标列表"""
        try:
            cart_pos_container = nrc.VectorDouble()
            ret_code = nrc.get_current_position(self.socket_fd, 1, cart_pos_container) #
            if ret_code == 0:
                return [cart_pos_container[i] for i in range(len(cart_pos_container))]
            else:
                print(f"获取笛卡尔位置失败，返回码: {ret_code}")
                return None
        except Exception as e:
            print(f"获取笛卡尔位置时发生异常: {e}")
            return None

    def run_motion_sequence(self):
        """执行运动测试序列"""
        print("\n--- 开始执行运动测试序列 ---")

        # 1. 获取初始位姿
        start_pos = self.get_cartesian_position()
        if not start_pos:
            print("错误：无法获取初始位置，测试中止。")
            return None
        
        print(f"1. 初始位姿 (X,Y,Z,A,B,C): {[f'{p:.3f}' for p in start_pos]}")
        
        current_pos = list(start_pos) # 创建一个可修改的副本

        # 2. 准备运动指令对象
        move_cmd = nrc.MoveCmd() #
        move_cmd.velocity = 30  # 设置一个安全的速度 (mm/s 或 a/s)
        move_cmd.acc = 20
        move_cmd.dec = 20
        move_cmd.pl = 1  # 平滑度，用1代替0，运动更柔和
        move_cmd.coord = 1 # 使用笛卡尔坐标系

        # 3. 逐轴移动
        try:
            # --- X轴移动 ---
            print("\n2. 正在向 X+ 方向移动 5mm...")
            target_x_pos = list(current_pos)
            target_x_pos[0] += 20.0
            move_cmd.targetPosValue = nrc.VectorDouble(target_x_pos) #
            ret_code = nrc.robot_movel(self.socket_fd, move_cmd) #
            print(f"  -> X轴移动指令发送完成，返回码: {ret_code}")
            time.sleep(2) # 等待运动完成
            current_pos = self.get_cartesian_position()
            print(f"  -> X轴移动后位姿: {[f'{p:.3f}' for p in current_pos]}")


            # --- Y轴移动 ---
            print("\n3. 正在向 Y+ 方向移动 5mm...")
            target_y_pos = list(current_pos)
            target_y_pos[1] += 20.0
            move_cmd.targetPosValue = nrc.VectorDouble(target_y_pos) #
            ret_code = nrc.robot_movel(self.socket_fd, move_cmd) #
            print(f"  -> Y轴移动指令发送完成，返回码: {ret_code}")
            time.sleep(2) # 等待运动完成
            current_pos = self.get_cartesian_position()
            print(f"  -> Y轴移动后位姿: {[f'{p:.3f}' for p in current_pos]}")


            # --- Z轴移动 ---
            print("\n4. 正在向 Z+ 方向移动 5mm...")
            target_z_pos = list(current_pos)
            target_z_pos[2] += 20.0
            move_cmd.targetPosValue = nrc.VectorDouble(target_z_pos) #
            ret_code = nrc.robot_movel(self.socket_fd, move_cmd) #
            print(f"  -> Z轴移动指令发送完成，返回码: {ret_code}")
            time.sleep(2) # 等待运动完成
            
            # 4. 获取并返回最终位姿
            final_pos = self.get_cartesian_position()
            if final_pos:
                print(f"\n5. ✅ 运动序列完成！最终位姿: {[f'{p:.3f}' for p in final_pos]}")
                return final_pos
            else:
                print("错误：无法获取最终位姿。")
                return None
        
        except Exception as e:
            print(f"运动序列执行中发生异常: {e}")
            return None

    def robot_power_off(self):
        """机器人安全下电"""
        print("\n--- 开始机器人下电流程 ---")
        try:
            nrc.set_servo_poweroff(self.socket_fd) #
            time.sleep(1)
            print("✅ 机器人已安全下电。")
        except Exception as e:
            print(f"下电流程中发生异常: {e}")


def main():
    """主测试函数"""
    robot = None
    print("=" * 60)
    print("           机 器 人 运 动 功 能 测 试")
    print("=" * 60)
    print("\n⚠️  警告：请确保机器人周围有足够的安全空间，测试即将开始！\n")
    time.sleep(3)

    try:
        # 连接机器人
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)
        motion_tester = RobotMotionTester(robot)

        # 步骤1: 初始化并上电
        if not motion_tester.robot_initialize_and_power_on():
            print("\n❌ 机器人未能成功上电，运动测试无法继续。")
            return

        # 步骤2: 执行运动序列
        motion_tester.run_motion_sequence()

    except Exception as e:
        print(f"\n❌ 测试过程中发生意外错误: {e}")
    # 替换 main 函数中的 finally 部分
    finally:
        # 步骤3: 确保机器人下电并断开连接
        if robot: # 确保 robot 对象已成功创建
            # *** 此处为关键修正点 ***
            # 使用 nrc.get_connection_status 来判断连接状态
            if nrc.get_connection_status(robot.socket_fd) == 0: # 假设0为已连接
                # 仅在连接状态下尝试下电
                motion_tester.robot_power_off()
            
            # 无论如何都执行断开连接
            robot.disconnect()
        print("\n测试结束。")

if __name__ == "__main__":
    main()